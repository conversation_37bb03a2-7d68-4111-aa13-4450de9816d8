# The main code
NS=$1
ESYM=$2
DUR=$3
i=$i
TEMPDIR="tempPM"

echo -e "$NS, $ESYM\n---"

while ((i < "$DUR"));
do
	kubectl -n $NS exec -it $ESYM -- bash -c 'ls -1 /coma/working_dir/notifs/pm_queue/'|while read FILE
	do
        NEWFILE=$(echo $FILE|sed 's/\r//g')
	set -x
    	kubectl -n $NS cp $ESYM:/coma/working_dir/notifs/pm_queue/$NEWFILE $NEWFILE
	python3 cne_pm_utils.py $NEWFILE --no-metrics >> PM_analysis.txt
        if [ -f "$NEWFILE" ]; then
           rm -f "$NEWFILE"
        fi
	rm -rf "$TEMPDIR" 
	done
i=$((i+1))
sleep 3600
echo $i
done



#!/usr/bin/python3

import gzip
import pickle
import re
import sys
from pathlib import Path
from datetime import datetime
import shutil
import pandas as pd
import xmltodict

# Filters for searching in DN
default_filters = ["ngss", "cfed", "amc", "bgc", "afed", "dfed", "hfed", "pfed"]
class MergXMLlogs:
    @staticmethod
    def merge_split_xmls(output_dir):
        """
        Merges XML files from pm_split that have the same first startTime and interval.
        """
        from collections import defaultdict

        # Mapping: (startTime, interval) -> list of files
        group_map = defaultdict(list)

        for file in Path(output_dir).glob("PM.*.xml.gz"):
            with gzip.open(file, "rt", encoding="utf-8") as f:
                content = f.read()
            # Get first startTime and interval
            pmsetup_match = re.search(r'<PMSetup[^>]*startTime="([^"]+)"[^>]*interval="([^"]+)"', content)
            if pmsetup_match:
                start_time = pmsetup_match.group(1)
                interval = pmsetup_match.group(2)
                group_map[(start_time, interval)].append(file)

        # Merge files in groups
        for key, files in group_map.items():
            if len(files) < 2:
                continue

            merged_pmsetups = []
            id_parts = []
            for idx, file in enumerate(files):
                # Extract ID from file name
                id_match = re.findall(r'ID\d+', file.name)
                id_parts.extend(id_match)
                with gzip.open(file, "rt", encoding="utf-8") as f:
                    lines = f.readlines()
                # Remove headers and footers
                body_lines = []
                for line in lines:
                    if line.strip().startswith('<?xml') or line.strip().startswith('<OMeS'):
                        continue
                    if line.strip().startswith('</OMeS>'):
                        continue
                    body_lines.append(line)
                merged_pmsetups.extend(body_lines)

            # Remove duplicate IDs and keep order
            id_parts = list(dict.fromkeys(id_parts))
            id_suffix = "_".join(id_parts) if id_parts else "IDunknown"

            # Build new file
            merged_content = '<?xml version="1.0"?>\n<OMeS version="2.3">\n'
            merged_content += ''.join(merged_pmsetups)
            merged_content += '</OMeS>\n'

            # Name of the new file with IDs
            merged_name = f"PM.{key[0].replace(':','-')}_interval{key[1]}_{id_suffix}.merged.xml.gz"
            merged_path = Path(output_dir) / merged_name
            with gzip.open(merged_path, "wt", encoding="utf-8") as gzfile:
                gzfile.write(merged_content)
            print(f"Created file: {merged_path}")

class SpliterPMLogs:
    """
    Tool for splitting and filtering PM logs from XML files (including .gz) according to specified filters.
    Methods:
        contains_any_filter(dn_text, filters):
            Checks if any filter is present in the given DN text.
        remove_xml_comments(text):
            Removes XML comments from the text.
        split_on_notifications_and_save(xml_path_str, filters=None):
            Splits the XML file into notification sections, filters them according to the filter list, and saves them as separate files.
    """
    def contains_any_filter(self, dn_text, filters):
        return any(filt in dn_text for filt in filters)

    def remove_xml_comments(self, text):
        return re.sub(r'<!--.*?-->', '', text, flags=re.DOTALL)

    def split_on_notifications_and_save(self, xml_path_str, output_dir=None, filters=None):
        xml_path = Path(xml_path_str)
        if output_dir is None:
            output_dir = xml_path.parent / "tempPM"
        output_dir.mkdir(parents=True, exist_ok=True)


        if filters is None:
            filters = default_filters

        if xml_path.suffix == ".gz":
            with gzip.open(xml_path, mode='rt', encoding='utf-8') as f:
                raw_text = f.read()
        else:
            raw_text = xml_path.read_text(encoding="utf-8")
        clean_text = self.remove_xml_comments(raw_text)

        sections = re.split(r'<--#NOTIFICATION#|-->\s*', clean_text)

        output_index = 0
        for section in sections:
            notif_match = re.search(r'#NOTIFICATION#(\d+)#(\d+)', section)
            if notif_match:
                unix_time = int(notif_match.group(1))
                notif_id = notif_match.group(2)
                dt = datetime.fromtimestamp(unix_time).strftime("%Y-%m-%d_%H-%M-%S")
                dt_id = f"{dt}_ID{notif_id}"
            else:
                dt_id = "unknown_time_IDunknown"

            pmsetup_matches = re.findall(r'<PMSetup[^>]*>.*?</PMSetup>', section, re.DOTALL)
            if not pmsetup_matches:
                continue

            filtered_pmsetups = []
            for pmsetup in pmsetup_matches:
                dn_match = re.search(r'<DN>(.*?)</DN>', pmsetup)
                if dn_match and self.contains_any_filter(dn_match.group(1), filters):
                    filtered_pmsetups.append(pmsetup)

            if not filtered_pmsetups:
                continue

            full_xml = '<?xml version="1.0"?>\n<OMeS version="2.3">\n'
            full_xml += '\n'.join(filtered_pmsetups)
            full_xml += '\n</OMeS>\n'

            filename = f"PM.{dt_id}.xml.gz"
            output_file = output_dir / filename
            with gzip.open(output_file, 'wt', encoding='utf-8') as gzfile:
                gzfile.write(full_xml)

            print(f"Saved: {output_file}")
            output_index += 1

        print(f"\nDone. Created {output_index} files.")


class SingleXml():
    """
    SingleXml is a class for analyzing XML files (including .gz) with PM data, extracting CPU and memory metrics for nodes matching the filter (default: "SIGi-cngss").
    Attributes:
        _pm_setups: list of PMSetup objects from the XML file,
        _cnfs: provided context,
        target_filter: search filter,
        specific_targets: list of dictionaries with DN and metrics.
    Methods:
        __init__: loads and processes the XML file,
        extract_specific_targets: extracts metrics for matching nodes,
        handle: processes PMSetup.
    """
    def __init__(self, xml_file, _cnfs, target_filter="SIGi-cngss"):
        """ __init__ """

        self._pm_setups = []
        self._cnfs = _cnfs
        self.target_filter = target_filter

        if (xml_file.suffix == ".gz"):
            with gzip.open(xml_file, "rt", encoding="utf-8") as fileptr:
                xml_content = fileptr.read()
        else:
            xml_content = xml_file.read_text(encoding="utf-8")

        xml_dict = xmltodict.parse(xml_content)
        pm_setup = xml_dict["OMeS"]["PMSetup"]
        if type(pm_setup) is list:
            for _pmsetup in pm_setup:
                self._pm_setups.append(PMSetup(_pmsetup))
        else:
            self._pm_setups.append(PMSetup(pm_setup))

        # Extract specific targets
        self.specific_targets = self.extract_specific_targets(xml_dict)

    def extract_specific_targets(self, xml_dict):
        """ Extract specific nodes based on the target_filter and corresponding aveBaseCpuUsage, peakBaseCpuUsage and memUsage """
        targets = []
        pm_setup = xml_dict["OMeS"]["PMSetup"]
        if not isinstance(pm_setup, list):
            pm_setup = [pm_setup]

        for setup in pm_setup:
            pm_results = setup.get("PMMOResult", [])
            if not isinstance(pm_results, list):
                pm_results = [pm_results]

            for result in pm_results:
                dn = result["MO"].get("DN", "")

                if self.target_filter in dn:
                    pm_target = result.get("PMTarget", {})
                    ave_cpu = pm_target.get("aveBaseCpuUsage", None)
                    peak_cpu = pm_target.get("peakBaseCpuUsage", None)
                    mem_usage = pm_target.get("memUsage", None)

                    # Only include targets where at least one metric is valid
                    if ave_cpu not in (None, "None") or peak_cpu not in (None, "None") or mem_usage not in (None, "None"):
                        # Update existing target or append a new one
                        existing_target = next((t for t in targets if t["DN"] == dn), None)
                        if existing_target:
                            if ave_cpu not in (None, "None"):
                                existing_target["aveBaseCpuUsage"] = ave_cpu
                            if peak_cpu not in (None, "None"):
                                existing_target["peakBaseCpuUsage"] = peak_cpu
                            if mem_usage not in (None, "None"):
                                existing_target["memUsage"] = mem_usage
                        else:
                            targets.append({"DN": dn,
                                            "aveBaseCpuUsage": ave_cpu,
                                            "peakBaseCpuUsage": peak_cpu,
                                            "memUsage": mem_usage})
        return targets

    def handle(self):
        for pm_setup in self._pm_setups:
            pm_setup.handle(self._cnfs)

class PMSetup():
    def __init__(self, _pmsetup):
        """ __init__ """
        self._starttime = _pmsetup["@startTime"]
        self._interval = _pmsetup["@interval"]
        self._mo_result = PMMOResult(_pmsetup["PMMOResult"])
    def handle(self, _cnfs):
        add_pm_to_cnfs(self, _cnfs)
    def get_cnf(self):
        return self._mo_result.get_cnf()
    def get_cnfc(self):
        return self._mo_result.get_cnfc()
    def get_component(self):
        return self._mo_result.get_component()
    def get_measurementType(self):
        return self._mo_result.get_measurementType()
    def get_counters(self):
        return self._mo_result.get_counters()
    def get_starttime(self):
        return self._starttime.split("+")[0]

class PMMOResult():
    """
    PMMOResult class parses and encapsulates the result of a Performance Management (PM) Managed Object (MO) query.

    Attributes:
        _dn (str): The distinguished name (DN) string from the MO result, representing the hierarchical path of the object.
        _cnf (str): The configuration name, extracted as the first element from the DN.
        _cnfc (str or None): The configuration component, extracted as the third element from the DN, or None if not present.
        _component (str or None): The component part of the DN, extracted as the fourth element, or None if not present.
        _target (PMTarget): An instance of PMTarget initialized with the "PMTarget" part of the MO result.

    Methods:
        get_cnf(): Returns the configuration name (_cnf).
        get_cnfc(): Returns the configuration component (_cnfc).
        get_component(): Returns the component part of the DN (_component).
        get_measurementType(): Returns the measurement type from the PMTarget.
        get_counters(): Returns the counters from the PMTarget.

    Args:
        _mo_result (dict): Dictionary containing the MO result, expected to have "MO" with "DN" and "PMTarget" keys.

    Raises:
        None explicitly, but sets attributes to None if the DN format is unexpected.
    """

    def __init__(self, _mo_result):
        """ __init__ """
        # CSBC-vsbcs05/SIG-1/SIGi-ksc4a0vsbc0005c-cngss-6fcd9c77ff-6mdbj/COMP-pcsf-prim_imsx-009/IF-Rx/ROLE-Term/PRI-No
        self._dn = _mo_result["MO"]["DN"]
        dn_els = self._dn.split("/", 3)
        self._cnf = dn_els[0]

        if len(dn_els) > 2:
            self._cnfc = dn_els[2]
        #    print(f"Warning: GOOD DN format, dn_els={dn_els}")
        else:
            self._cnfc = None  # print error
            # print(f"Warning: Unexpected DN format, dn_els={dn_els}")
            return

        self._component = dn_els[3] if len(dn_els) > 3 else None
        self._target = PMTarget(_mo_result["PMTarget"])
    def get_cnf(self):
        return self._cnf
    def get_cnfc(self):
        return self._cnfc
    def get_component(self):
        return self._component
    def get_measurementType(self):
        return self._target.get_measurementType()
    def get_counters(self):
        return self._target.get_counters()

class PMTarget():
    def __init__(self, _target):
        self._measurementType = _target["@measurementType"]
        self._counters = {}
        for key, value in _target.items():
            if key != "@measurementType":
                self._counters[key] = value
    def get_measurementType(self):
        return self._measurementType
    def get_counters(self):
        return self._counters

def add_pm_to_cnfs(_setup, _cnfs):
    """ add new pm set to given cnf """
    dn_cnf = _setup.get_cnf()
    if dn_cnf in _cnfs.keys():
        _cnf = _cnfs[dn_cnf]
    else:
        _cnf = Cnf(dn_cnf)
        _cnfs[dn_cnf] = _cnf

    _cnf.add_pm(_setup)

def convert_t_counters_to_df(t_counters):
    df = pd.DataFrame.from_dict(t_counters)
    df.index.name = "counter"
    df.columns.name = None
    return df

def combine_t_counters(tc1, tc2):
    tc3 = {}
    for t in set(tc1.keys()) & set(tc2.keys()):
        tc3[t] = combine_counters(tc1[t], tc2[t])
    for t in set(tc1.keys()) ^ set(tc2.keys()):
        if t in tc1.keys():
            tc3[t] = tc1[t]
        else:
            tc3[t] = tc2[t]
    return tc3

def combine_counters(c1, c2):
    c3 = {}
    for key in set(c1.keys()) & set(c2.keys()):
        c3[key] = float(c1[key]) + float(c2[key])
    for key in set(c1.keys()) ^ set(c2.keys()):
        if key in c1.keys():
            c3[key] = float(c1[key])
        else:
            c3[key] = float(c2[key])
    return c3

# def get_cnfc_type(cnfc_name):
#     # SIGi-ksc4a0vsbc0005c-cngss-6fcd9c77ff-rj9mg
#     return (cnfc_name.split("-")[2])

def get_cnfc_type(cnfc_name):
    if not cnfc_name:
        # print("Error: cnfc_name is None!")
        return None
    parts = cnfc_name.split("-")
    if len(parts) > 2:
        return parts[2]
    else:
        print(f"Warning: Unexpected cnfc_name format: {cnfc_name}")
        return None

class Cnf():
    def __init__(self, name):
        self.name = name
        self.cnfc_type_list = []
        self.cnfcs = {}           # key is cnfc name, value is Cnfc object
        self._df = None
        # Print only if print_metrics is True
        if globals().get("print_metrics", True):
            print(f"handling new cnf: {name}")

    def get_cnfc_by_name(self, cnfc_name):
        if cnfc_name in self.cnfcs.keys():
            _cnfc = self.cnfcs[cnfc_name]
        else:
            _cnfc = Cnfc(cnfc_name)
            self.cnfcs[cnfc_name] = _cnfc
        return _cnfc

    def add_pm(self, _setup):
        # e.g. get cngss-6fcd9c77ff-rj9mg for SIGi-ksc4a0vsbc0005c-cngss-6fcd9c77ff-rj9mg
        dn_cnfc = _setup.get_cnfc()
        # e.g. cngss
        dn_cnfc_type = get_cnfc_type(dn_cnfc)
        # print(f"INFO: dn_cnfc_type: {dn_cnfc_type}")
        if not dn_cnfc_type:
            return
        if dn_cnfc_type not in self.cnfc_type_list:
            self.cnfc_type_list.append(dn_cnfc_type)

        # get corresponding object for incoming cnfc
        _cnfc = self.get_cnfc_by_name(dn_cnfc)
        _cnfc.add_pm(_setup)

    # TODO: consider to move it to sbc_utils.py
    def _generate_compound_counters(self, _df):
        """ Generate compound counters """
        if _df is None:
            print("df is None, _generate_compound_counters can't be made")
            return

        df = _df.T
        out_df = pd.DataFrame(index = df.index)

        pm_list = df.columns

        if ("PCSCFUEOrigSIPINVRecPAT" in pm_list) and ("PCSCFUETermSIPINVRecPAT" in pm_list):
            out_df["@Call_attempt_rate"] = df["PCSCFUEOrigSIPINVRecPAT"] + df["PCSCFUETermSIPINVRecPAT"]
        elif ("PCSCFUEOrigSIPINVRecPAT" in pm_list):
            out_df["@Call_attempt_rate"] = df["PCSCFUEOrigSIPINVRecPAT"]
        elif ("PCSCFUETermSIPINVRecPAT" in pm_list):
            out_df["@Call_attempt_rate"] = df["PCSCFUETermSIPINVRecPAT"]
        else:
            print("@Call_attempt_rate not populated")

        return out_df.T

    def set_pm_df(self, df):
        self._df = df

    def get_pm_df(self):
        if self._df is not None:
            return self._df

        # Generate Aggregate counters
        t_counters = {}
        for name, cnfc in self.cnfcs.items():
            cnfc_df = cnfc.get_pm_df()

            cnfc_df["cnfc"] = name
            # Concat cnfc df to cnf df to store all data in cnf level
            self._df = pd.concat([self._df, cnfc_df], join='outer')

            # This is to calculate Aggregate counters on cnf level (i.e. sum of cnfc counters with same name)
            tmp_t_counters = cnfc.c_t_counters["Aggregate"]
            t_counters = combine_t_counters(t_counters, tmp_t_counters)
            # cnfc level data can be removed now.
            cnfc._df = None
            cnfc.c_t_counters = None

        aggr_df = convert_t_counters_to_df(t_counters)
        aggr_df["comp"] = "Aggregate"
        aggr_df["cnfc"] = "Aggregate"

        compound_df = self._generate_compound_counters(aggr_df)
        compound_df["comp"] = "Aggregate"
        compound_df["cnfc"] = "Aggregate"

        # Concat aggregate and compound df to main df
        self._df = pd.concat([self._df, aggr_df, compound_df], join='outer')

        self._df.index.name = "counter"
        self._df.set_index(["comp", "cnfc"], append=True, inplace=True)
        # df has 3 indices: cnfc, comp, counter
        self._df = self._df.swaplevel(0, 2)
        self._df.sort_index(inplace=True)

        return self._df

    def convert_cnfc_comp_name(self, cnfc=None, comp=None):
        if cnfc is None:
            cnfc = "Aggregate"
            comp = "Aggregate"
        elif comp is None:
            comp = "Aggregate"
        return cnfc, comp

    def get_pm_list(self, cnfc=None, comp=None):
        if self._df is None:
            self.get_pm_df()
        cnfc, comp = self.convert_cnfc_comp_name(cnfc, comp)
        print(f"{cnfc}, {comp}")
        df = self._df.loc[(cnfc, comp)]
        return list(df.index.values)

    def get_pm_by_name(self, name_list, cnfc=None, comp=None):
        if self._df is None:
            self.get_pm_df()
        cnfc, comp = self.convert_cnfc_comp_name(cnfc, comp)
        try:
            df = self._df.loc[(cnfc, comp)]

            df_names = list(df.index.values)
            # glob handling
            tmp_name_list = list()
            for name in name_list:
                if name.endswith("*") and name.find("="):
                    sub_str = name.strip("*")
                    tmp_name_list = [df_name for df_name in df_names if df_name.startswith(sub_str)]

            name_list.extend(tmp_name_list)

            # TODO:
            # Do we need to return the pm list to caller to be aware?
            good_keys = df.index.intersection(name_list)
            df_good_keys = df.loc[good_keys]
            return df.loc[good_keys]
        except Exception as ex:
            print(f"failed on get_pm_by_name, {ex}")
            return pd.DataFrame()

    def get_pm_by_subtype(self, cnfc=None, comp=None):
        if self._df is None:
            self.get_pm_df()
        # df.loc[(slice(None),"one"), :]
        cnfc, comp = self.convert_cnfc_comp_name(cnfc, comp)
        df = self._df.loc[(cnfc, comp)]
        return df

class Cnfc():
    """CNFC: Collects and aggregates PM counters by component and timestamp.
    Attributes:
        name: Name of the CNFC.
        t_counters: Counters by timestamp.
        c_t_counters: Counters by component and timestamp.
        comps: List of component names.
        _df: Cached DataFrame.
    Methods:
        _convert_comp: Normalizes component/counter names.
        add_pm: Adds PM data.
        _add_pm: Internal method for adding data.
        get_pm_df: Returns a DataFrame with PM data and aggregates.
    """
    def __init__(self, name):
        self.name = name
        self.t_counters = {}  # counters with timestamp
        self.c_t_counters = {}  # counters with timestamp with component
        self.comps = list()
        self._df = None

    def _convert_comp(self, dn_comp, counters):
        """
        handle component with error code e.g., COMP-pcsf-prim_imsx-005/ERRC3-401/ATYPE-OTHER-TYPE
        move 401 to counter name, e.g. PCSCFErrorCodeREREGRespSent_errorCode=401
        remove ERRC3-401 from component name
        """
        out_counters = {}
        if dn_comp is not None:
            els = dn_comp.split("/", 3)
            if len(els) == 1 and (els[0].startswith("TIMER-") or els[0].startswith("EIF-") or els[0].startswith("IF-")):
                els[0] = els[0].replace("-", "=", 1)
                out_dn_comp = None
                for key, value in counters.items():
                    new_key = f"{key}_{els[0]}"
                    out_counters[new_key] = value
            elif len(els) > 1 and els[1].startswith("ERRC"):
                error_code = els[1].split("-")[1]
                del els[1]
                out_dn_comp = "/".join(els)
                # print(f"new comp_name: {self.name}")
                out_counters = {}
                for key, value in counters.items():
                    new_key = f"{key}_errCode={error_code}"
                    out_counters[new_key] = value
            elif len(els) > 1 and els[1].startswith("IF-"):
                interface_type = els[1].split("-")[1]
                del els[1]
                cause = None
                if len(els) > 1 and els[1].startswith("CAUSE-"):
                    cause = els[1].split("-")[1]
                    del els[1]
                out_dn_comp = "/".join(els)
                out_counters = {}
                if cause is None:
                    suffix = f"IF={interface_type}"
                else:
                    suffix = f"IF={interface_type}_CAUSE={cause}"
                for key, value in counters.items():
                    new_key = f"{key}_{suffix}"
                    out_counters[new_key] = value
            # elif len(els) > 1 and els[1].startswith("ATYPE-"):
                # access_type = els[1].split("-", 1)[1]
                # del els[1]
                # out_dn_comp = "/".join(els)
                # # print(f"handling comp {dn_comp}, rename to {out_dn_comp}")
                # # print(f"comp_name: {out_dn_comp}")
                # out_counters = {}
                # for key, value in counters.items():
                #     new_key = f"{key}_accessType={access_type}"
                #     out_counters[new_key] = value
            else:
                out_dn_comp, out_counters = dn_comp, counters
        else:
            out_dn_comp, out_counters = dn_comp, counters

        out2_counters = {}
        for key, value in out_counters.items():
            if key.startswith("SIP") or key.startswith("ADNS"):
                pool_type_suffix = "PoolType=" + get_cnfc_type(self.name)
                new_key = key + "_" + pool_type_suffix
                out2_counters[new_key] = value
            else:
                out2_counters[key] = value

        return out_dn_comp, out2_counters

    def add_pm(self, _setup):
        dn_comp = _setup.get_component()
        # 2022-12-07T14:35
        start_time = _setup.get_starttime()
        counters = _setup.get_counters()

        # if dn_comp is not None:
        # do convert on component name and counter names if necessary
        dn_comp, counters = self._convert_comp(dn_comp, counters)

        self._add_pm(start_time, counters, dn_comp)

    def _add_pm(self, start_time, counters, comp):
        if comp is None:
            comp = ""

        # component exist, attach counters to component
        if comp not in self.comps:
            # print(f"add new comp {comp}")
            self.comps.append(comp)

        if comp in self.c_t_counters.keys() and start_time in self.c_t_counters[comp].keys():
            self.c_t_counters[comp][start_time].update(counters)
        elif comp in self.c_t_counters.keys() and start_time not in self.c_t_counters[comp].keys():
            self.c_t_counters[comp][start_time] = counters
        else:
            _dict = {}
            _dict[start_time] = counters
            self.c_t_counters[comp] = _dict

    def get_pm_df(self):
        if self._df is not None:
            return self._df

        # Generate Aggregate counters
        t_counters = {}
        for comp in self.c_t_counters.keys():
            tmp_t_counters = self.c_t_counters[comp]
            t_counters = combine_t_counters(t_counters, tmp_t_counters)
        self.c_t_counters["Aggregate"] = t_counters

        self.c_t_counters["Aggregate"] = t_counters
        for comp in self.c_t_counters.keys():
            tmp_t_counters = self.c_t_counters[comp]
            tmp_df = convert_t_counters_to_df(tmp_t_counters)
            tmp_df["comp"] = comp
            self._df = pd.concat([self._df, tmp_df], join='outer')
        return self._df

def dump_cnfcs(_cnfs, fpath):
    with open(fpath, 'wb') as f:
        pickle.dump(_cnfs, f)
        print(f"cnfs saved to {fpath}")

def load_cnfcs(fpath):
    with open(fpath, 'rb') as f:
        _cnfs = pickle.load(f)
        print(f"loaded cnfs from {fpath}")
        return _cnfs

if __name__ == "__main__":
    """
    Main entry point for the PM Counter Dump Tool.

    Processes PM logs (.log, .xml, .xml.gz), supporting splitting, filtering, and analysis of PM data for specified targets.
    Extracts CPU and memory metrics, aggregates counters, and generates a summary report.

    Usage:
        python script.py <path_to_file> [optional_XML_file_to_analyze] [optional_filter] [--no-metrics]

    Arguments:
        <path_to_file>: Input file (.log, .xml, .xml.gz).
        [optional_XML_file_to_analyze]: (Optional) Specific XML file to analyze after splitting.
        [optional_filter]: (Optional) Filter string to match DN.
        [--no-metrics]: (Optional) If present, do not print metrics to terminal.

    Flow:
        - Validates input and file existence.
        - For .log: splits and merges notifications, prepares XML for analysis.
        - For .xml/.xml.gz: analyzes directly.
        - For each file: extracts metrics and prints a summary to the terminal (unless --no-metrics is set).
    """

    # Handling the --no-metrics flag
    print_metrics = True
    if "--no-metrics" in sys.argv:
        print_metrics = False
        sys.argv.remove("--no-metrics")

    # Check the correctness of the number of arguments (without --no-metrics)
    if len(sys.argv) < 2 or len(sys.argv) > 4:
        print("Usage: python script.py <path_to_file> [optional_XML_file_to_analyze] [optional_filter] [--no-metrics]")
        sys.exit(1)

    input_path_str = sys.argv[1]
    input_path = Path(input_path_str)
    if not input_path.exists():
        print(f"File {input_path_str} does not exist.")
        sys.exit(1)

    # Path to an optional single file for analysis (if provided)
    single_xml_path = None
    filter_arg = None

    if len(sys.argv) >= 3:
        possible_path = Path(sys.argv[2])
        if possible_path.exists():
            single_xml_path = possible_path
            if len(sys.argv) == 4:
                filter_arg = sys.argv[3]
        else:
            filter_arg = sys.argv[2]

    # Optional filter argument
    if len(sys.argv) == 4:
        filter_arg = sys.argv[3]
    elif len(sys.argv) == 3 and (single_xml_path is None or not single_xml_path.exists()):
        filter_arg = sys.argv[2]

    # Now, check file extension and decide what to do
    files_to_analyze = []
    if input_path.suffix == ".log":
        # Create Folders
        temp_dir = input_path.parent / "tempPM"
        pm_output_dir = input_path.parent / "pm_output"
        # Create tempPM if it doesn't exist and clear it
        if temp_dir.exists():
            for f in temp_dir.glob("*"):
                if f.is_file():
                    f.unlink()
                elif f.is_dir():
                    shutil.rmtree(f)
        else:
            temp_dir.mkdir(parents=True, exist_ok=True)

        # Split into tempPM
        spliter = SpliterPMLogs()
        spliter.split_on_notifications_and_save(input_path_str, output_dir=temp_dir)

        # Merge from tempPM
        MergXMLlogs.merge_split_xmls(temp_dir)

        # Create pm_output if it doesn't exist
        pm_output_dir.mkdir(parents=True, exist_ok=True)

        # Move files from tempPM to pm_output
        for f in temp_dir.glob("*"):
            shutil.move(str(f), pm_output_dir / f.name)

        # Process only .xml.gz files (excluding .merged.xml.gz), but if --no-metrics is set, do not process any files
        if print_metrics:
            files_to_analyze = sorted(
                f for f in pm_output_dir.glob("*.xml.gz") if not f.name.endswith(".merged.xml.gz")
            )
        else:
            files_to_analyze = []
    elif input_path.suffix in [".xml", ".gz"]:
        if single_xml_path and single_xml_path.exists():
            files_to_analyze = [single_xml_path]
        else:
            files_to_analyze = [input_path]
    else:
        print("Supported input file extensions: .log, .xml, .xml.gz")
        sys.exit(1)

    if not files_to_analyze:
        print("No files to analyze.")
        sys.exit(0)

for file_path in files_to_analyze:
    if print_metrics:
        print(f"\nAnalyzing file {file_path.name}:")
    cnfs = {}
    # Use filter_arg if provided, otherwise default_filters
    filters_to_use = [filter_arg] if filter_arg else default_filters
    for filter_value in filters_to_use:
        single_xml = SingleXml(Path(file_path), cnfs, filter_value)
        single_xml.handle()
        for target in single_xml.specific_targets:
            ave_cpu = float(target['aveBaseCpuUsage']) if target['aveBaseCpuUsage'] not in (None, "None") else 0.0
            peak_cpu = float(target['peakBaseCpuUsage']) if target['peakBaseCpuUsage'] not in (None, "None") else 0.0
            mem_usage = float(target['memUsage']) if target['memUsage'] not in (None, "None") else 0.0
            line = (f"{'':<15} DN={target['DN']:<46} "
                    f"aveBaseCpuUsage = {ave_cpu:>6.3f} "
                    f"peakBaseCpuUsage = {peak_cpu:>6.3f} "
                    f"memUsage = {mem_usage:>6.3f}")
            if print_metrics:
                print(line)
print("Processing finished.")
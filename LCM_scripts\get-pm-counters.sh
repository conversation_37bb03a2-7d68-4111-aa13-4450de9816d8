#!/bin/bash


if [ -z "$1" ]; then
  NS="ncp0113"
else
  NS="$1"
fi

if [ -z "$2" ]; then
  VER="25"
else
  VER="$2"
fi

echo "NS: $NS"
echo "VER: $VER"

ESYMAC_LIST=$(kubectl -n ${NS} get pods | grep esymac | cut -d ' ' -f1)

for ESYMAC in ${ESYMAC_LIST}
do
        echo ${ESYMAC}
        mkdir ${ESYMAC}

        if [ $VER == 25 ]; then
                FILES=$(kubectl -n ${NS} exec -it ${ESYMAC} -c esymacservice -- ls -tr /coma/working_dir/notifs/pm_queue/ | tail -10)
        else
                FILES=$(kubectl -n ${NS} exec -it ${ESYMAC} -c esymacservice -- ls -tr /ESYMAC/R_ESYMAC_18.9_MP1_snmp4j/extras/esymacstarter/gmoTemp/pm | tail -10)
        fi


        for FILE in ${FILES}
        do
                echo ${FILE}
                FILE="${FILE%%[[:cntrl:]]}"

                if [ $VER == 25 ]; then
                        kubectl -n ${NS} cp ${ESYMAC}:/coma/working_dir/notifs/pm_queue/${FILE} ${ESYMAC}/${FILE} -c esymacservice
                else
                        kubectl -n ${NS} cp ${ESYMAC}:/ESYMAC/R_ESYMAC_18.9_MP1_snmp4j/extras/esymacstarter/gmoTemp/pm/${FILE} ${ESYMAC}/${FILE} -c esymacservice
                fi
        done

        echo
        echo


done

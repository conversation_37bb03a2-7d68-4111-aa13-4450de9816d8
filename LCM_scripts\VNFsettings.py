import argparse
import subprocess
import os
import pty
import io
import json
import re


class KubectlPseudoTerminal:
    def __init__(self, namespace, pod_name):
        self.namespace = namespace
        self.pod_name = pod_name

    def __enter__(self):
        self.master_fd, self.slave_fd = pty.openpty()
        self.process = subprocess.Popen(
            ["kubectl", "exec", "-n", self.namespace, "-it", self.pod_name, "--", "bash"],
            stdin=self.slave_fd, stdout=self.slave_fd, stderr=self.slave_fd, close_fds=True
        )
        self.tty = io.TextIOWrapper(os.fdopen(self.master_fd, 'rb+', buffering=0), line_buffering=True)
        return self.tty

    def __exit__(self, exception_type, exception_value, exception_traceback):
        self.process.terminate()
        self.tty.close()


def parse_args():
    parser = argparse.ArgumentParser(description='Check VNF settings and MAJOR/MINOR version for all <NAME_EMAIL>')
    parser.add_argument('--namespace', '-n', help='Signaling namespace', type=str)
    parser.add_argument('--25.7', help='Set version as 25.7', dest='v25_7', action='store_true')
    parser.add_argument('--username', help='Username for ztssettings1 container', type=str)
    parser.add_argument('--password', help='Password for ztssettings1 container', type=str)
    parser.set_defaults(v25_7=False, username='zts1user', password='R00tR@@t')
    return parser.parse_args()


def execute_commands(commands):
    process = subprocess.Popen([commands], shell=True, stdin=subprocess.PIPE, stdout=subprocess.PIPE)
    output, _ = process.communicate()
    return output.decode()


def get_full_pod_name(namespace, default_pod_name, check_redisio_role=None):
    if check_redisio_role:
        pods_info = execute_commands('kubectl get pod ' + '-L redisio_role' + ' -n ' + namespace + '| grep ' + default_pod_name)
        for line in pods_info.split('\n'):
            if check_redisio_role in line:
                pod_info = line
                break
    else:
        pod_info = execute_commands('kubectl get pod -n ' + namespace + '| grep ' + default_pod_name)
    full_pod_name = pod_info.split()[0]
    return full_pod_name


def get_vnf_set_res_msg(namespace, pod_name, username, password):
    with KubectlPseudoTerminal(namespace, pod_name) as pt:
        line = pt.readline()
        while 'ztssettings1' not in line or not line:
            line = pt.readline()
        pt.write('cd /opt/SettingsCli; ./settingscli -list VNFSettings\n')
        pt.flush()
        while 'Username' not in line or not line:
            line = pt.readline()
        pt.write(username + '\n')
        pt.flush()
        while 'Password' not in line or not line:
            line = pt.readline()
        pt.write(password + '\n')
        pt.flush()
        output = ''
        while 'vnf_product_family' not in line or not line:
            line = pt.readline()
            output += line
    return output


def get_vnf_settings_as_string(namespace, username, password):
    pod_name = get_full_pod_name(namespace, 'integration')
    info = get_vnf_set_res_msg(namespace, pod_name, username, password)
    return info


def parse_vnf_settings(to_parse):
    json_part = re.search(r'\n\{.*\}', to_parse, re.DOTALL)
    data = json.loads(json_part.group(0))
    return data['VNFSetResMsg']


def get_all_vnf_names(vnf_set_res_msg_list):
    vnf_names_list = []
    for vnf_msg in vnf_set_res_msg_list:
        vnf_names_list.append(vnf_msg['VnfName'])
    return vnf_names_list


def get_crdb_password(namespace, version25_7):
    pod_name = get_full_pod_name(namespace, 'ztscmserver')
    password_path = '/opt/crdb/acl/password'
    if version25_7:
        password_path = '/opt/.crdb/.acl/zts_rx_upw'
    crdb_password = execute_commands("kubectl exec -n " + namespace + " " + pod_name + " -- bash -c 'cat " + password_path + "'")
    return crdb_password.strip()


def get_version_for_each_vnf(namespace, pod_name, crdb_password, vnf_names_list):
    vnfs_info = {}
    redis_command = 'redis-cli -c --tls --cert /var/run/certs/client/tls.crt --key /var/run/certs/client/tls.key --cacert /var/run/certs/cacerts/server-ca.crt --user cmappuser --pass ' + crdb_password
    with KubectlPseudoTerminal(namespace, pod_name) as pt:
        for vnf_name in vnf_names_list:
            cm_metadata_version = 'major:'
            pt.write(redis_command + ' HGET {ZTSCM}-CM-HM-' + vnf_name + ' MAJOR\n')
            pt.flush()
            line = pt.readline()
            while '"' not in line or not line:
                line = pt.readline()
            cm_metadata_version += line.replace('"', '').strip()
            cm_metadata_version += '|minor:'
            pt.write(redis_command + ' HGET {ZTSCM}-CM-HM-' + vnf_name + ' MINOR\n')
            pt.flush()
            line = pt.readline()
            while '"' not in line or not line:
                line = pt.readline()
            cm_metadata_version += line.replace('"', '').strip()
            vnfs_info[vnf_name] = cm_metadata_version
    return vnfs_info


def get_major_minor_versions(namespace, version25_7, vnf_names_list):
    crdb_password = get_crdb_password(namespace, version25_7)
    pod_name = get_full_pod_name(namespace, 'zts-crdb', check_redisio_role='master')
    return get_version_for_each_vnf(namespace, pod_name, crdb_password, vnf_names_list)


def compare_versions(vnf_set_res_msg_list, mm_versions_for_vnf_names):
    different_versions = False
    for vnf_msg in vnf_set_res_msg_list:
        vnf_name = vnf_msg['VnfName']
        print('CmMetadataVersion for ' + vnf_name + ' in VNFSettings: ' + vnf_msg['CmMetadataVersion'])
        print('CmMetadataVersion for ' + vnf_name + ' in CRDB: ' + mm_versions_for_vnf_names[vnf_name] + '\n')
        if vnf_msg['CmMetadataVersion'] != mm_versions_for_vnf_names[vnf_name]:
            different_versions = True
    if different_versions:
        print('Versions are different!!!')
    else:
        print('Versions are the same! :D')


def main():
    args = parse_args()
    vnf_settings = get_vnf_settings_as_string(args.namespace, args.username, args.password)
    print(vnf_settings)
    vnf_set_res_msg_list = parse_vnf_settings(vnf_settings)
    vnf_names_list = get_all_vnf_names(vnf_set_res_msg_list)
    mm_versions_for_vnf_names = get_major_minor_versions(args.namespace, args.v25_7, vnf_names_list)
    print()
    compare_versions(vnf_set_res_msg_list, mm_versions_for_vnf_names)


if __name__ == "__main__":
    main()

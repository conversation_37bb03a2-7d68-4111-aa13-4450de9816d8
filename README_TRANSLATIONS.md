# Polish to English Translations

This document lists the files that have been translated from Polish to English in the Tools directory.

## Translated Files

### LCM_scripts Directory

1. **download_debug_logs.py** → **download_debug_logs_en.py**
   - Interactive tool for downloading debug log files from Kubernetes pods
   - Features: Download by file number, number range, date range, time period, or all files
   - Automatically compresses downloaded files to tar.gz format

2. **download_archive_logs.py** → **download_archive_logs_en.py**
   - Interactive tool for downloading archive log files from Kubernetes pods  
   - Similar features to debug logs tool but for archive logs location
   - Supports date-based filtering and compression

3. **monitor-pods.sh** → **monitor-pods_en.sh**
   - Bash script for monitoring Kubernetes pods in real-time
   - Displays pod status changes with timestamps
   - Prompts for namespace input if not provided as argument

## Translation Summary

### Key Polish Terms Translated:
- "Podaj namespace" → "Enter namespace"
- "Lista plików" → "File list"
- "Pobierz" → "Download"
- "Zakres" → "Range"
- "<PERSON><PERSON><PERSON><PERSON> opcję" → "Choose option"
- "Nieprawidłowy" → "Invalid"
- "Zakończ<PERSON>" → "Finished"
- "Błąd" → "Error"
- "Pliki zostały skompresowane" → "Files have been compressed"
- "Folder roboczy został usunięty" → "Working folder has been deleted"

### Files Not Requiring Translation:
- **VNFsettings.py** - Contains only English comments and code
- **get-pm-counters.sh** - Bash script with no Polish content
- **dump_PMs_v2.sh** - Bash script with no Polish content  
- **cne_pm_utils.py** - Python utility with English documentation
- **PM_counter_dump_tool_V2.pptx** - PowerPoint presentation (not text-based)

## Usage Notes

The English versions maintain all original functionality while providing English language prompts and messages. All file paths, command structures, and logic remain identical to ensure compatibility with existing systems.

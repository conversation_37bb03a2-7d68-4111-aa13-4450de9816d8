
import os
import subprocess
from datetime import datetime, timedelta
import re
import tarfile

# Funkcja do pobrania listy plików z Kubernetes
def fetch_file_list(namespace):
    try:
        cmd = ["kubectl", "-n", namespace, "exec", "logsreceiver-0", "--", "ls", "/opt/logstore/archive/logs"]
        file_list = subprocess.check_output(cmd, universal_newlines=True)
        return file_list
    except subprocess.CalledProcessError as e:
        print(f"Blad podczas pobierania listy plików: {e}")
        return ""

# Funkcja do wyciągania daty z nazwy pliku (w formacie YYYY-MM-DD_HH-MM-SS)
def extract_datetime(fname):
    match = re.search(r'(\d{4})[_](\d{2})[_](\d{2})T(\d{2})[_](\d{2})[_](\d{2})', fname)
    if match:
        date_str = f"{match.group(1)}-{match.group(2)}-{match.group(3)} {match.group(4)}:{match.group(5)}:{match.group(6)}"
        try:
            return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        except Exception:
            return None
    return None

# Funkcja do parsowania listy plików i wyciągania dostępnych dat
def parse_files(file_output):
    all_files = file_output.splitlines()
    available_dates = sorted(set(
        extract_datetime(file) for file in all_files if extract_datetime(file) is not None
    ))
    return all_files, available_dates

# Funkcja do wyświetlania wszystkich plików z numerami
def show_files_with_numbers(all_files):
    print("\nLista plików:")
    for index, file in enumerate(all_files, 1):
        print(f"{index}. {file}")
    print(f"\nZakres plików: 1 - {len(all_files)}")

# Funkcja do kopiowania pliku z Kubernetes do lokalnego systemu
def copy_file_to_local(namespace, file_name, local_dir="debug_logs"):
    if not os.path.exists(local_dir):
        os.makedirs(local_dir)

    cmd = [
        "kubectl", "-n", namespace, "cp",
        f"{namespace}/logsreceiver-0:/opt/logstore/archive/logs/{file_name}",
        f"./{local_dir}/{file_name}"
    ]
    try:
        subprocess.check_output(cmd)
        print(f"Plik {file_name} został skopiowany do folderu {local_dir}")
    except subprocess.CalledProcessError:
        print(f"Blad podczas kopiowania pliku {file_name}. Sprawdz namespace/pod.")

# Funkcja do kopiowania plików na podstawie numerów
def copy_files_by_number(namespace, range_str, all_files, local_dir="debug_logs"):
    try:
        start_num, end_num = map(int, range_str.split('-'))
        if 1 <= start_num <= end_num <= len(all_files):
            files_to_copy = all_files[start_num-1:end_num]
            for file_name in files_to_copy:
                copy_file_to_local(namespace, file_name, local_dir)
        else:
            print("Nieprawidłowy zakres numerów.")
    except ValueError:
        print("Nieprawidłowy format zakresu. Użyj formatu np. 25-30.")

# Funkcja do kopiowania plików na podstawie daty
def copy_files_by_date(namespace, start_date, end_date, all_files, local_dir="debug_logs"):
    date_filtered_files = [
        file for file in all_files
        if start_date <= extract_datetime(file) <= end_date
    ]

    for file_name in date_filtered_files:
        copy_file_to_local(namespace, file_name, local_dir)

# Funkcja do kopiowania plików z ostatnich 'x' godzin
def copy_files_last_hours(namespace, time_period, all_files, local_dir="debug_logs"):
    # Jeśli czas jest podany w minutach (np. 30m), konwertujemy to na godziny i minuty
    if time_period.endswith('m'):
        minutes = int(time_period[:-1])
        hours = minutes // 60
        minutes = minutes % 60
    else:
        hours = int(time_period)
        minutes = 0

    # Obliczamy czas początkowy
    current_time = datetime.now()
    start_time = current_time - timedelta(hours=hours, minutes=minutes)

    # Filtrujemy pliki z ostatnich 'x' godzin/minut
    filtered_files = [
        file for file in all_files
        if extract_datetime(file) and extract_datetime(file) >= start_time
    ]

    print(f"Pobieram pliki z ostatnich {hours} godzin i {minutes} minut:")
    for file_name in filtered_files:
        copy_file_to_local(namespace, file_name, local_dir)

# Funkcja do kompresji plików do TAR.GZ
def compress_files_to_tar_gz(local_dir="debug_logs"):
    tar_name = f"{local_dir}.tar.gz"
    with tarfile.open(tar_name, 'w:gz') as tarf:
        for root, dirs, files in os.walk(local_dir):
            for file in files:
                tarf.add(os.path.join(root, file), os.path.relpath(os.path.join(root, file), local_dir))
    print(f"Pliki zostały skompresowane do: {tar_name}")

# Funkcja do usuwania folderu roboczego
def remove_local_dir(local_dir="debug_logs"):
    if os.path.exists(local_dir):
        for root, dirs, files in os.walk(local_dir, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(local_dir)
        print(f"Folder roboczy {local_dir} został usunięty.")

# Funkcja do wyświetlania menu
def show_menu():
    print("""
    ==== MENU ====
    1. Pokaż wszystkie pliki
    2. Pobierz plik po numerze
    3. Pobierz pliki po zakresie numerów (np. 25-30)
    4. Pobierz pliki po zakresie dat (i godzin)
    5. Pobierz pliki z ostatnich 'x' godzin lub minut (np. 30m)
    0. Wyjście
    """)

# Funkcja do pytania o powrót do menu
def prompt_for_back_to_menu():
    while True:
        choice = input("\nAby wrócić do menu głównego, naciśnij 'm', lub naciśnij Enter, aby kontynuować: ").strip().lower()
        if choice == 'm':
            return True
        else:
            return False

# Główna funkcja
def main():
    namespace = input("Podaj namespace [domyślnie: ncp0113]: ").strip()
    if not namespace:
        namespace = "ncp0113"

    # Pobranie listy plików
    file_output = fetch_file_list(namespace)
    if not file_output.strip():
        print("Brak danych do wyświetlenia.")
        return

    # Parsowanie plików
    all_files, available_dates = parse_files(file_output)

    # Wyświetlenie najwcześniejszej i najpóźniejszej daty
    if available_dates:
        min_date = min(available_dates)
        max_date = max(available_dates)
        print(f"\nZakres dat: {min_date.strftime('%Y-%m-%d %H:%M:%S')} - {max_date.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print("Brak dostępnych dat.")

    # Generowanie nazwy folderu na podstawie godziny zapisu
    current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    local_dir = f"debug_logs_{current_time}"

    # Tworzymy folder roboczy
    if not os.path.exists(local_dir):
        os.makedirs(local_dir)

    # Interaktywne menu
    while True:
        show_menu()
        choice = input("Wybierz opcję: ")

        if choice == "1":
            show_files_with_numbers(all_files)
            continue  # od razu przejdź do następnej iteracji
        elif choice == "2":
            show_files_with_numbers(all_files)
            file_number = int(input("Podaj numer pliku: ")) - 1
            if 0 <= file_number < len(all_files):
                copy_file_to_local(namespace, all_files[file_number], local_dir)
                compress_files_to_tar_gz(local_dir)
                remove_local_dir(local_dir)
            else:
                print("Nieprawidłowy numer pliku.")
            continue  # od razu przejdź do następnej iteracji
        elif choice == "3":
            range_str = input("Podaj zakres numerów plików (np. 25-30): ").strip()
            copy_files_by_number(namespace, range_str, all_files, local_dir)
            compress_files_to_tar_gz(local_dir)
            remove_local_dir(local_dir)
            continue  # od razu przejdź do następnej iteracji
        elif choice == "4":
            print("\nDostępne daty:")
            if available_dates:
                min_date = min(available_dates)
                max_date = max(available_dates)
                print(f"Zakres dat: {min_date.strftime('%Y-%m-%d %H:%M:%S')} - {max_date.strftime('%Y-%m-%d %H:%M:%S')}")

            start_date = input("\nPodaj początkową datę i godzinę (YYYY-MM-DD HH:MM:SS): ")
            end_date = input("Podaj końcową datę i godzinę (YYYY-MM-DD HH:MM:SS): ")

            try:
                dt_from = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
                dt_to = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
                copy_files_by_date(namespace, dt_from, dt_to, all_files, local_dir)
                compress_files_to_tar_gz(local_dir)
                remove_local_dir(local_dir)
            except ValueError:
                print("Nieprawidłowy format daty!")
            continue  # od razu przejdź do następnej iteracji
        elif choice == "5":
            time_period = input("Ile ostatnich godzin lub minut chcesz pobrać? (np. 30m, 2h): ").strip()
            copy_files_last_hours(namespace, time_period, all_files, local_dir)
            compress_files_to_tar_gz(local_dir)
            remove_local_dir(local_dir)
            continue  # od razu przejdź do następnej iteracji
        elif choice == "0":
            print("Zakończono.")
            break
        else:
            print("Nieprawidłowy wybór. Spróbuj ponownie.")
            continue  # od razu przejdź do następnej iteracji

if __name__ == "__main__":
    main()


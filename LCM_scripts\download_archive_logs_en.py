import os
import subprocess
from datetime import datetime, timedelta
import re
import tarfile

# Function to fetch file list from Kubernetes
def fetch_file_list(namespace):
    try:
        cmd = ["kubectl", "-n", namespace, "exec", "logsreceiver-0", "--", "ls", "/opt/logstore/archive/logs"]
        file_list = subprocess.check_output(cmd, universal_newlines=True)
        return file_list
    except subprocess.CalledProcessError as e:
        print(f"Error fetching file list: {e}")
        return ""

# Function to extract date from filename (format YYYY-MM-DD_HH-MM-SS)
def extract_datetime(fname):
    match = re.search(r'(\d{4})[_](\d{2})[_](\d{2})T(\d{2})[_](\d{2})[_](\d{2})', fname)
    if match:
        date_str = f"{match.group(1)}-{match.group(2)}-{match.group(3)} {match.group(4)}:{match.group(5)}:{match.group(6)}"
        try:
            return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
        except Exception:
            return None
    return None

# Function to parse file list and extract available dates
def parse_files(file_output):
    all_files = file_output.splitlines()
    available_dates = sorted(set(
        extract_datetime(file) for file in all_files if extract_datetime(file) is not None
    ))
    return all_files, available_dates

# Function to display all files with numbers
def show_files_with_numbers(all_files):
    print("\nFile list:")
    for index, file in enumerate(all_files, 1):
        print(f"{index}. {file}")
    print(f"\nFile range: 1 - {len(all_files)}")

# Function to copy file from Kubernetes to local system
def copy_file_to_local(namespace, file_name, local_dir="debug_logs"):
    if not os.path.exists(local_dir):
        os.makedirs(local_dir)

    cmd = [
        "kubectl", "-n", namespace, "cp",
        f"{namespace}/logsreceiver-0:/opt/logstore/archive/logs/{file_name}",
        f"./{local_dir}/{file_name}"
    ]
    try:
        subprocess.check_output(cmd)
        print(f"File {file_name} has been copied to folder {local_dir}")
    except subprocess.CalledProcessError:
        print(f"Error copying file {file_name}. Check namespace/pod.")

# Function to copy files based on numbers
def copy_files_by_number(namespace, range_str, all_files, local_dir="debug_logs"):
    try:
        start_num, end_num = map(int, range_str.split('-'))
        if 1 <= start_num <= end_num <= len(all_files):
            files_to_copy = all_files[start_num-1:end_num]
            for file_name in files_to_copy:
                copy_file_to_local(namespace, file_name, local_dir)
        else:
            print("Invalid number range.")
    except ValueError:
        print("Invalid range format. Use format like 25-30.")

# Function to copy files based on date
def copy_files_by_date(namespace, start_date, end_date, all_files, local_dir="debug_logs"):
    date_filtered_files = [
        file for file in all_files
        if start_date <= extract_datetime(file) <= end_date
    ]

    for file_name in date_filtered_files:
        copy_file_to_local(namespace, file_name, local_dir)

# Function to copy files from last 'x' hours
def copy_files_last_hours(namespace, time_period, all_files, local_dir="debug_logs"):
    # If time is given in minutes (e.g. 30m), convert to hours and minutes
    if time_period.endswith('m'):
        minutes = int(time_period[:-1])
        hours = minutes // 60
        minutes = minutes % 60
    else:
        hours = int(time_period)
        minutes = 0

    # Calculate start time
    current_time = datetime.now()
    start_time = current_time - timedelta(hours=hours, minutes=minutes)

    # Filter files from last 'x' hours/minutes
    filtered_files = [
        file for file in all_files
        if extract_datetime(file) and extract_datetime(file) >= start_time
    ]

    print(f"Downloading files from the last {hours} hours and {minutes} minutes:")
    for file_name in filtered_files:
        copy_file_to_local(namespace, file_name, local_dir)

# Function to compress files to TAR.GZ
def compress_files_to_tar_gz(local_dir="debug_logs"):
    tar_name = f"{local_dir}.tar.gz"
    with tarfile.open(tar_name, 'w:gz') as tarf:
        for root, dirs, files in os.walk(local_dir):
            for file in files:
                tarf.add(os.path.join(root, file), os.path.relpath(os.path.join(root, file), local_dir))
    print(f"Files have been compressed to: {tar_name}")

# Function to remove working folder
def remove_local_dir(local_dir="debug_logs"):
    if os.path.exists(local_dir):
        for root, dirs, files in os.walk(local_dir, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(local_dir)
        print(f"Working folder {local_dir} has been deleted.")

# Function to display menu
def show_menu():
    print("""
    ==== MENU ====
    1. Show all files
    2. Download file by number
    3. Download files by number range (e.g. 25-30)
    4. Download files by date range (and time)
    5. Download files from last 'x' hours or minutes (e.g. 30m)
    0. Exit
    """)

# Function to prompt for returning to menu
def prompt_for_back_to_menu():
    while True:
        choice = input("\nTo return to main menu, press 'm', or press Enter to continue: ").strip().lower()
        if choice == 'm':
            return True
        else:
            return False

# Main function
def main():
    namespace = input("Enter namespace [default: ncp0113]: ").strip()
    if not namespace:
        namespace = "ncp0113"

    # Fetch file list
    file_output = fetch_file_list(namespace)
    if not file_output.strip():
        print("No data to display.")
        return

    # Parse files
    all_files, available_dates = parse_files(file_output)

    # Display earliest and latest date
    if available_dates:
        min_date = min(available_dates)
        max_date = max(available_dates)
        print(f"\nDate range: {min_date.strftime('%Y-%m-%d %H:%M:%S')} - {max_date.strftime('%Y-%m-%d %H:%M:%S')}")
    else:
        print("No available dates.")

    # Generate folder name based on save time
    current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    local_dir = f"debug_logs_{current_time}"

    # Create working folder
    if not os.path.exists(local_dir):
        os.makedirs(local_dir)

    # Interactive menu
    while True:
        show_menu()
        choice = input("Choose option: ")

        if choice == "1":
            show_files_with_numbers(all_files)
            continue  # go directly to next iteration
        elif choice == "2":
            show_files_with_numbers(all_files)
            file_number = int(input("Enter file number: ")) - 1
            if 0 <= file_number < len(all_files):
                copy_file_to_local(namespace, all_files[file_number], local_dir)
                compress_files_to_tar_gz(local_dir)
                remove_local_dir(local_dir)
            else:
                print("Invalid file number.")
            continue  # go directly to next iteration
        elif choice == "3":
            range_str = input("Enter file number range (e.g. 25-30): ").strip()
            copy_files_by_number(namespace, range_str, all_files, local_dir)
            compress_files_to_tar_gz(local_dir)
            remove_local_dir(local_dir)
            continue  # go directly to next iteration
        elif choice == "4":
            print("\nAvailable dates:")
            if available_dates:
                min_date = min(available_dates)
                max_date = max(available_dates)
                print(f"Date range: {min_date.strftime('%Y-%m-%d %H:%M:%S')} - {max_date.strftime('%Y-%m-%d %H:%M:%S')}")

            start_date = input("\nEnter start date and time (YYYY-MM-DD HH:MM:SS): ")
            end_date = input("Enter end date and time (YYYY-MM-DD HH:MM:SS): ")

            try:
                dt_from = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
                dt_to = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
                copy_files_by_date(namespace, dt_from, dt_to, all_files, local_dir)
                compress_files_to_tar_gz(local_dir)
                remove_local_dir(local_dir)
            except ValueError:
                print("Invalid date format!")
            continue  # go directly to next iteration
        elif choice == "5":
            time_period = input("How many last hours or minutes do you want to download? (e.g. 30m, 2h): ").strip()
            copy_files_last_hours(namespace, time_period, all_files, local_dir)
            compress_files_to_tar_gz(local_dir)
            remove_local_dir(local_dir)
            continue  # go directly to next iteration
        elif choice == "0":
            print("Finished.")
            break
        else:
            print("Invalid choice. Try again.")
            continue  # go directly to next iteration

if __name__ == "__main__":
    main()

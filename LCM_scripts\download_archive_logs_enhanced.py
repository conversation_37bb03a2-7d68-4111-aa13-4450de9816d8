#!/usr/bin/env python3
"""
Enhanced Archive Logs Download Script
Improved version with better error handling, logging, and user experience
"""

import os
import subprocess
from datetime import datetime, timedelta
import re
import tarfile
import sys
from typing import List, Optional, Tuple
from common_utils import (
    validate_namespace, validate_pod_exists, safe_kubectl_exec,
    create_backup_directory, log_operation, confirm_operation,
    format_file_size, validate_date_format, check_disk_space_requirements
)


class ArchiveLogsDownloader:
    def __init__(self, namespace: str = "ncp0113"):
        self.namespace = namespace
        self.pod_name = "logsreceiver-0"
        self.remote_path = "/opt/logstore/archive/logs"
        self.local_dir = None
        
    def validate_environment(self) -> bool:
        """Validate namespace and pod existence"""
        log_operation("Validating environment", f"Namespace: {self.namespace}")
        
        if not validate_namespace(self.namespace):
            print(f"❌ Error: Namespace '{self.namespace}' does not exist")
            return False
            
        if not validate_pod_exists(self.namespace, self.pod_name):
            print(f"❌ Error: Pod '{self.pod_name}' not found in namespace '{self.namespace}'")
            return False
            
        print(f"✅ Environment validation passed")
        return True
    
    def fetch_file_list(self) -> Tuple[bool, List[str]]:
        """Fetch file list from Kubernetes with error handling"""
        log_operation("Fetching file list", f"from {self.pod_name}")
        
        cmd = ["kubectl", "-n", self.namespace, "exec", self.pod_name, "--", "ls", self.remote_path]
        success, stdout, stderr = safe_kubectl_exec(cmd)
        
        if not success:
            print(f"❌ Error fetching file list: {stderr}")
            return False, []
            
        files = [line.strip() for line in stdout.splitlines() if line.strip()]
        log_operation("File list fetched", f"Found {len(files)} files")
        return True, files
    
    def extract_datetime(self, filename: str) -> Optional[datetime]:
        """Extract datetime from filename with improved regex"""
        # Support multiple date formats
        patterns = [
            r'(\d{4})[_](\d{2})[_](\d{2})T(\d{2})[_](\d{2})[_](\d{2})',
            r'(\d{4})-(\d{2})-(\d{2})_(\d{2})-(\d{2})-(\d{2})',
            r'(\d{4})_(\d{2})_(\d{2})_(\d{2})_(\d{2})_(\d{2})'
        ]
        
        for pattern in patterns:
            match = re.search(pattern, filename)
            if match:
                date_str = f"{match.group(1)}-{match.group(2)}-{match.group(3)} {match.group(4)}:{match.group(5)}:{match.group(6)}"
                try:
                    return datetime.strptime(date_str, "%Y-%m-%d %H:%M:%S")
                except ValueError:
                    continue
        return None
    
    def parse_files(self, files: List[str]) -> Tuple[List[str], List[datetime]]:
        """Parse files and extract available dates"""
        available_dates = []
        valid_files = []
        
        for file in files:
            dt = self.extract_datetime(file)
            if dt:
                available_dates.append(dt)
                valid_files.append(file)
        
        available_dates = sorted(set(available_dates))
        return valid_files, available_dates
    
    def show_files_with_numbers(self, files: List[str], show_size: bool = False):
        """Display files with numbers and optional size information"""
        print(f"\n📁 File list ({len(files)} files):")
        print("-" * 80)
        
        for index, file in enumerate(files, 1):
            dt = self.extract_datetime(file)
            date_str = dt.strftime("%Y-%m-%d %H:%M:%S") if dt else "Unknown date"
            size_info = ""
            
            if show_size:
                # Get file size from remote
                cmd = ["kubectl", "-n", self.namespace, "exec", self.pod_name, "--", 
                       "stat", "-c", "%s", f"{self.remote_path}/{file}"]
                success, stdout, stderr = safe_kubectl_exec(cmd)
                if success and stdout.strip().isdigit():
                    size_bytes = int(stdout.strip())
                    size_info = f" ({format_file_size(size_bytes)})"
            
            print(f"{index:3d}. {file}{size_info}")
            print(f"     📅 {date_str}")
        
        print(f"\n📊 File range: 1 - {len(files)}")
    
    def copy_file_to_local(self, filename: str) -> bool:
        """Copy file from Kubernetes to local system with progress indication"""
        if not self.local_dir:
            print("❌ Error: Local directory not initialized")
            return False
        
        local_path = os.path.join(self.local_dir, filename)
        
        # Check if file already exists
        if os.path.exists(local_path):
            if not confirm_operation(f"File {filename} already exists. Overwrite? (y/N): "):
                print(f"⏭️  Skipping {filename}")
                return True
        
        log_operation("Copying file", filename)
        
        cmd = [
            "kubectl", "-n", self.namespace, "cp",
            f"{self.namespace}/{self.pod_name}:{self.remote_path}/{filename}",
            local_path
        ]
        
        success, stdout, stderr = safe_kubectl_exec(cmd, timeout=60)
        
        if success:
            print(f"✅ {filename} copied successfully")
            return True
        else:
            print(f"❌ Error copying {filename}: {stderr}")
            return False
    
    def copy_files_by_number(self, range_str: str, files: List[str]) -> bool:
        """Copy files by number range with validation"""
        try:
            if '-' in range_str:
                start_num, end_num = map(int, range_str.split('-'))
            else:
                start_num = end_num = int(range_str)
            
            if not (1 <= start_num <= end_num <= len(files)):
                print(f"❌ Invalid range: {range_str}. Valid range is 1-{len(files)}")
                return False
            
            files_to_copy = files[start_num-1:end_num]
            print(f"📋 Copying {len(files_to_copy)} files...")
            
            success_count = 0
            for i, filename in enumerate(files_to_copy, 1):
                print(f"\n📥 Progress: {i}/{len(files_to_copy)}")
                if self.copy_file_to_local(filename):
                    success_count += 1
            
            print(f"\n✅ Successfully copied {success_count}/{len(files_to_copy)} files")
            return success_count == len(files_to_copy)
            
        except ValueError:
            print(f"❌ Invalid range format: {range_str}. Use format like '25-30' or '25'")
            return False
    
    def copy_files_by_date(self, start_date: str, end_date: str, files: List[str]) -> bool:
        """Copy files by date range with validation"""
        if not validate_date_format(start_date):
            print(f"❌ Invalid start date format: {start_date}")
            return False
        
        if not validate_date_format(end_date):
            print(f"❌ Invalid end date format: {end_date}")
            return False
        
        try:
            dt_from = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
            dt_to = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
        except ValueError:
            print("❌ Invalid date format. Use YYYY-MM-DD HH:MM:SS")
            return False
        
        date_filtered_files = [
            file for file in files
            if dt_from <= self.extract_datetime(file) <= dt_to
        ]
        
        if not date_filtered_files:
            print("❌ No files found in specified date range")
            return False
        
        print(f"📋 Found {len(date_filtered_files)} files in date range")
        return self._copy_file_list(date_filtered_files)
    
    def copy_files_last_hours(self, time_period: str, files: List[str]) -> bool:
        """Copy files from last X hours/minutes"""
        # Parse time period
        if time_period.endswith('m'):
            try:
                minutes = int(time_period[:-1])
                hours = minutes // 60
                minutes = minutes % 60
            except ValueError:
                print(f"❌ Invalid time format: {time_period}")
                return False
        elif time_period.endswith('h'):
            try:
                hours = int(time_period[:-1])
                minutes = 0
            except ValueError:
                print(f"❌ Invalid time format: {time_period}")
                return False
        else:
            try:
                hours = int(time_period)
                minutes = 0
            except ValueError:
                print(f"❌ Invalid time format: {time_period}")
                return False
        
        current_time = datetime.now()
        start_time = current_time - timedelta(hours=hours, minutes=minutes)
        
        filtered_files = [
            file for file in files
            if self.extract_datetime(file) and self.extract_datetime(file) >= start_time
        ]
        
        if not filtered_files:
            print(f"❌ No files found from last {hours}h {minutes}m")
            return False
        
        print(f"📋 Found {len(filtered_files)} files from last {hours}h {minutes}m")
        return self._copy_file_list(filtered_files)
    
    def _copy_file_list(self, files: List[str]) -> bool:
        """Helper method to copy a list of files"""
        print(f"📋 Copying {len(files)} files...")
        
        success_count = 0
        for i, filename in enumerate(files, 1):
            print(f"\n📥 Progress: {i}/{len(files)}")
            if self.copy_file_to_local(filename):
                success_count += 1
        
        print(f"\n✅ Successfully copied {success_count}/{len(files)} files")
        return success_count == len(files)
    
    def compress_files(self) -> bool:
        """Compress files to TAR.GZ with progress indication"""
        if not self.local_dir or not os.path.exists(self.local_dir):
            print("❌ Error: Local directory not found")
            return False
        
        tar_name = f"{self.local_dir}.tar.gz"
        log_operation("Compressing files", f"to {tar_name}")
        
        try:
            with tarfile.open(tar_name, 'w:gz') as tarf:
                for root, dirs, files in os.walk(self.local_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, self.local_dir)
                        tarf.add(file_path, arcname)
            
            print(f"✅ Files compressed to: {tar_name}")
            return True
        except Exception as e:
            print(f"❌ Error compressing files: {e}")
            return False
    
    def cleanup(self):
        """Clean up working directory"""
        if self.local_dir and os.path.exists(self.local_dir):
            log_operation("Cleaning up", self.local_dir)
            try:
                for root, dirs, files in os.walk(self.local_dir, topdown=False):
                    for name in files:
                        os.remove(os.path.join(root, name))
                    for name in dirs:
                        os.rmdir(os.path.join(root, name))
                os.rmdir(self.local_dir)
                print(f"✅ Working directory cleaned up")
            except Exception as e:
                print(f"⚠️  Warning: Could not clean up directory {self.local_dir}: {e}")
    
    def show_menu(self):
        """Display enhanced menu"""
        print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                        ARCHIVE LOGS DOWNLOADER                               ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  1. Show all files (with dates)                                             ║
║  2. Show all files (with sizes)                                             ║
║  3. Download file by number                                                  ║
║  4. Download files by number range (e.g. 25-30)                            ║
║  5. Download files by date range                                            ║
║  6. Download files from last X hours/minutes (e.g. 30m, 2h)                ║
║  0. Exit                                                                     ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """)
    
    def run(self):
        """Main execution method"""
        print("🚀 Enhanced Archive Logs Downloader")
        print("=" * 50)
        
        # Validate environment
        if not self.validate_environment():
            return
        
        # Fetch file list
        success, files = self.fetch_file_list()
        if not success:
            return
        
        # Parse files
        valid_files, available_dates = self.parse_files(files)
        
        if not valid_files:
            print("❌ No valid files found")
            return
        
        # Show date range
        if available_dates:
            min_date = min(available_dates)
            max_date = max(available_dates)
            print(f"\n📅 Available date range: {min_date.strftime('%Y-%m-%d %H:%M:%S')} - {max_date.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Create working directory
        self.local_dir = create_backup_directory("archive_logs")
        print(f"📁 Working directory: {self.local_dir}")
        
        # Main menu loop
        while True:
            self.show_menu()
            choice = input("Choose option: ").strip()
            
            if choice == "1":
                self.show_files_with_numbers(valid_files, show_size=False)
            elif choice == "2":
                self.show_files_with_numbers(valid_files, show_size=True)
            elif choice == "3":
                self.show_files_with_numbers(valid_files)
                try:
                    file_number = int(input("Enter file number: ")) - 1
                    if 0 <= file_number < len(valid_files):
                        if self.copy_file_to_local(valid_files[file_number]):
                            self.compress_files()
                            self.cleanup()
                    else:
                        print("❌ Invalid file number")
                except ValueError:
                    print("❌ Invalid input")
            elif choice == "4":
                range_str = input("Enter file number range (e.g. 25-30): ").strip()
                if self.copy_files_by_number(range_str, valid_files):
                    self.compress_files()
                    self.cleanup()
            elif choice == "5":
                print("\n📅 Available dates:")
                if available_dates:
                    min_date = min(available_dates)
                    max_date = max(available_dates)
                    print(f"Date range: {min_date.strftime('%Y-%m-%d %H:%M:%S')} - {max_date.strftime('%Y-%m-%d %H:%M:%S')}")
                
                start_date = input("\nEnter start date and time (YYYY-MM-DD HH:MM:SS): ")
                end_date = input("Enter end date and time (YYYY-MM-DD HH:MM:SS): ")
                
                if self.copy_files_by_date(start_date, end_date, valid_files):
                    self.compress_files()
                    self.cleanup()
            elif choice == "6":
                time_period = input("How many last hours or minutes? (e.g. 30m, 2h): ").strip()
                if self.copy_files_last_hours(time_period, valid_files):
                    self.compress_files()
                    self.cleanup()
            elif choice == "0":
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Try again.")


def main():
    """Main entry point"""
    try:
        namespace = input("Enter namespace [default: ncp0113]: ").strip() or "ncp0113"
        downloader = ArchiveLogsDownloader(namespace)
        downloader.run()
    except KeyboardInterrupt:
        print("\n\n⚠️  Operation cancelled by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main() 

import os
import subprocess
import re
from datetime import datetime, timedelta
import tarfile

def get_namespace():
    NS = input("Enter namespace (default 'ncp0113'): ") or "ncp0113"
    print(f"Namespace: {NS}")
    return NS

def list_debug_files(NS, pod, path):
    exec_cmd = [
        "kubectl", "-n", NS, "exec", "-it", pod, "--", "ls", "-1", path
    ]
    files_proc = subprocess.run(exec_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, check=True)
    files = [line.strip() for line in files_proc.stdout.strip().splitlines() if line.endswith('.tgz')]
    return files

def extract_datetime(fname):
    match = re.search(r'(\d{4})_(\d{2})_(\d{2})T(\d{2})_(\d{2})_(\d{2})', fname)
    if match:
        dt_str = f"{match.group(1)}-{match.group(2)}-{match.group(3)} {match.group(4)}:{match.group(5)}:{match.group(6)}"
        return datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
    return None

def get_min_max_dates(all_files):
    # Wyciągamy daty z nazw plików i znajdujemy najwcześniejszą i najpóźniejszą datę
    dates = [extract_datetime(file) for file in all_files if extract_datetime(file) is not None]
    if dates:
        min_date = min(dates)
        max_date = max(dates)
        return min_date, max_date
    return None, None

def show_menu():
    print("""
    ==== MENU ====
    1. Pokaż wszystkie pliki
    2. Pobierz plik po numerze
    3. Pobierz pliki po zakresie numerów (np. 25-30)
    4. Pobierz pliki po zakresie dat (i godzin)
    5. Pobierz pliki z ostatnich 'x' godzin lub minut (np. 30m)
    6. Pobierz wszystkie pliki debugowe
    0. Wyjście
    """)

def show_files_with_numbers(all_files):
    print("\nLista plików:")
    for index, file in enumerate(all_files, 1):
        print(f"{index}. {file}")
    print(f"\nZakres plików: 1 - {len(all_files)}")

def create_dynamic_working_directory():
    # Tworzymy katalog roboczy o nazwie 'debug_logs_<data i godzina>'
    current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    working_dir = f"debug_logs_{current_time}"
    if not os.path.exists(working_dir):
        os.makedirs(working_dir)
    return working_dir

def copy_file_to_local(NS, pod, file_name, local_dir):
    local_path = os.path.join(local_dir, os.path.basename(file_name))  # Ścieżka lokalna do zapisu pliku
    
    # Upewniamy się, że katalog roboczy istnieje
    if not os.path.exists(local_dir):
        os.makedirs(local_dir)
    
    cmd = [
        "kubectl", "-n", NS, "cp",
        f"{pod}:{file_name}",
        local_path  # Kopiujemy do lokalnej ścieżki
    ]
    try:
        subprocess.check_output(cmd)
        print(f"Plik {file_name} został skopiowany do folderu {local_path}")
    except subprocess.CalledProcessError as e:
        print(f"Blad podczas kopiowania pliku {file_name}. Sprawdz namespace/pod. {e}")

def compress_files_to_tar_gz(local_dir):
    local_path = os.path.join(os.getcwd(), local_dir)  # Zamiast "/opt/logstore", używamy lokalnej ścieżki

    tar_name = f"{local_path}.tar.gz"
    with tarfile.open(tar_name, 'w:gz') as tarf:
        for root, dirs, files in os.walk(local_path):
            for file in files:
                tarf.add(os.path.join(root, file), os.path.relpath(os.path.join(root, file), local_path))
    print(f"Pliki zostały skompresowane do: {tar_name}")

def remove_local_dir(local_dir):
    local_path = os.path.join(os.getcwd(), local_dir)
    
    if os.path.exists(local_path):
        for root, dirs, files in os.walk(local_path, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(local_path)
        print(f"Folder roboczy {local_path} został usunięty.")
    else:
        print(f"Folder roboczy {local_path} nie istnieje.")

def get_files(NS, pod, path):
    files = list_debug_files(NS, pod, path)
    return files

def download_all_debug_files(NS, pod, path):
    # Tworzymy katalog roboczy z unikalną nazwą na podstawie daty i godziny
    local_dir = create_dynamic_working_directory()
    
    all_files = get_files(NS, pod, path)
    if not all_files:
        print("Brak plików do pobrania.")
        return
    
    for file in all_files:
        print(f"Pobieram plik {file}")
        copy_file_to_local(NS, pod, os.path.join(path, file), local_dir)  # Używamy pełnej ścieżki
    
    compress_files_to_tar_gz(local_dir)
    remove_local_dir(local_dir)

def copy_files_last_hours(NS, pod, time_period, all_files):
    # Tworzymy katalog roboczy z unikalną nazwą na podstawie daty i godziny
    local_dir = create_dynamic_working_directory()
    
    if time_period.endswith('m'):
        minutes = int(time_period[:-1])
        hours = minutes // 60
        minutes = minutes % 60
    else:
        hours = int(time_period)
        minutes = 0

    current_time = datetime.now()
    start_time = current_time - timedelta(hours=hours, minutes=minutes)

    filtered_files = [
        file for file in all_files
        if extract_datetime(file) and extract_datetime(file) >= start_time
    ]

    print(f"Pobieram pliki z ostatnich {hours} godzin i {minutes} minut:")
    for file_name in filtered_files:
        copy_file_to_local(NS, pod, file_name, local_dir)
    
    compress_files_to_tar_gz(local_dir)
    remove_local_dir(local_dir)

def copy_files_by_number(NS, pod, range_str, all_files, local_dir, path):
    try:
        # Parsowanie zakresu numerów
        start_num, end_num = map(int, range_str.split('-'))
        if 1 <= start_num <= end_num <= len(all_files):
            files_to_copy = all_files[start_num-1:end_num]
            for file_name in files_to_copy:
                print(f"Pobieram plik {file_name}")
                copy_file_to_local(NS, pod, os.path.join(path, file_name), local_dir)  # Kopiujemy pliki
        else:
            print("Nieprawidłowy zakres numerów.")
    except ValueError:
        print("Nieprawidłowy format zakresu. Użyj formatu np. 25-30.")

def main():
    NS = get_namespace()
    pod = "logsreceiver-0"
    path = "/opt/logstore/transfer/debug/"  # Zmieniamy tylko lokalizację w kontenerze

    while True:
        show_menu()
        choice = input("Wybierz opcję: ").strip()

        if choice == "1":
            # W punkcie 1 nie tworzymy katalogu roboczego, bo tylko listujemy pliki
            all_files = get_files(NS, pod, path)
            show_files_with_numbers(all_files)
        elif choice == "2":
            local_dir = create_dynamic_working_directory()
            all_files = get_files(NS, pod, path)
            show_files_with_numbers(all_files)
            file_number = int(input("Podaj numer pliku: ")) - 1
            if 0 <= file_number < len(all_files):
                copy_file_to_local(NS, pod, path + all_files[file_number], local_dir)
                compress_files_to_tar_gz(local_dir)
                remove_local_dir(local_dir)
            else:
                print("Nieprawidłowy numer pliku.")
        elif choice == "3":
            range_str = input("Podaj zakres numerów plików (np. 25-30): ").strip()
            local_dir = create_dynamic_working_directory()
            all_files = get_files(NS, pod, path)
            copy_files_by_number(NS, pod, range_str, all_files, local_dir, path)  # Przekazanie path
            compress_files_to_tar_gz(local_dir)
            remove_local_dir(local_dir)
        elif choice == "4":
            all_files = get_files(NS, pod, path)
            min_date, max_date = get_min_max_dates(all_files)

            if min_date and max_date:
                print(f"Zakres dostępnych dat: od {min_date} do {max_date}")
                start_date = input("Podaj początkową datę i godzinę (YYYY-MM-DD HH:MM:SS): ")
                end_date = input("Podaj końcową datę i godzinę (YYYY-MM-DD HH:MM:SS): ")
                local_dir = create_dynamic_working_directory()
                try:
                    dt_from = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
                    dt_to = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
                    copy_files_by_date(NS, pod, dt_from, dt_to, all_files, local_dir)
                    compress_files_to_tar_gz(local_dir)
                    remove_local_dir(local_dir)
                except ValueError:
                    print("Nieprawidłowy format daty!")
            else:
                print("Brak dostępnych plików do przetworzenia.")
        elif choice == "5":
            time_period = input("Ile ostatnich godzin lub minut chcesz pobrać? (np. 30m, 2h): ").strip()
            local_dir = create_dynamic_working_directory()
            all_files = get_files(NS, pod, path)
            copy_files_last_hours(NS, pod, time_period, all_files, local_dir)
            compress_files_to_tar_gz(local_dir)
            remove_local_dir(local_dir)
        elif choice == "6":
            # Punkt 6 teraz działa automatycznie, pobiera wszystkie pliki debugowe
            download_all_debug_files(NS, pod, path)
        elif choice == "0":
            print("Zakończono.")
            break
        else:
            print("Nieprawidłowy wybór. Spróbuj ponownie.")

if __name__ == "__main__":
    main()


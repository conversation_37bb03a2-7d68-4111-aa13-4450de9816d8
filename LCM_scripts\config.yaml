# LCM Scripts Configuration
# This file contains common settings for all LCM scripts

# Default namespace
default_namespace: "ncp0113"

# Pod configurations
pods:
  logsreceiver: "logsreceiver-0"
  integration: "integration"
  ztscmserver: "ztscmserver"
  zts_crdb: "zts-crdb"

# Path configurations
paths:
  archive_logs: "/opt/logstore/archive/logs"
  debug_logs: "/opt/logstore/transfer/debug"
  pm_queue_v25: "/coma/working_dir/notifs/pm_queue/"
  pm_queue_v18: "/ESYMAC/R_ESYMAC_18.9_MP1_snmp4j/extras/esymacstarter/gmoTemp/pm/"

# Authentication settings
auth:
  default_username: "zts1user"
  default_password: "R00tR@@t"

# File patterns for date extraction
date_patterns:
  - "(\d{4})[_](\d{2})[_](\d{2})T(\d{2})[_](\d{2})[_](\d{2})"
  - "(\d{4})-(\d{2})-(\d{2})_(\d{2})-(\d{2})-(\d{2})"
  - "(\d{4})_(\d{2})_(\d{2})_(\d{2})_(\d{2})_(\d{2})"

# Timeout settings (seconds)
timeouts:
  kubectl_exec: 30
  file_copy: 60
  compression: 120

# Logging settings
logging:
  level: "INFO"
  include_timestamps: true
  show_progress: true

# UI settings
ui:
  show_emojis: true
  use_colors: true
  confirm_operations: true

# Backup settings
backup:
  auto_compress: true
  auto_cleanup: true
  preserve_working_dir: false 
# LCM Scripts - Enhanced Version

This directory contains enhanced versions of the LCM (Life Cycle Management) scripts with improved error handling, logging, and user experience.

## 🚀 New Features

### Enhanced Error Handling
- **Namespace validation**: Checks if namespace exists before operations
- **Pod validation**: Verifies pod availability
- **Timeout handling**: Prevents hanging operations
- **Graceful error recovery**: Better error messages and recovery options

### Improved User Experience
- **Progress indicators**: Visual feedback during operations
- **File size display**: Shows file sizes when listing
- **Confirmation prompts**: Asks before overwriting files
- **Enhanced menus**: Better formatted and more intuitive interfaces

### Better Logging
- **Timestamped operations**: All operations are logged with timestamps
- **Operation tracking**: Progress tracking for long operations
- **Error logging**: Detailed error information for troubleshooting

## 📁 File Structure

```
LCM_scripts/
├── common_utils.py              # Shared utilities and error handling
├── config.yaml                  # Centralized configuration
├── download_archive_logs_enhanced.py  # Enhanced archive logs downloader
├── download_debug_logs_enhanced.py    # Enhanced debug logs downloader
├── download_archive_logs_en.py        # Original English version
├── download_archive_logs.py           # Original non-English version
├── download_debug_logs_en.py          # Original English version
├── download_debug_logs.py             # Original non-English version
├── get-pm-counters.sh                 # PM counters script (fixed)
├── monitor-pods_en.sh                 # Pod monitoring (English)
├── monitor-pods.sh                    # Pod monitoring (non-English)
├── VNFsettings.py                     # VNF settings checker
└── README_ENHANCED.md                # This file
```

## 🔧 Fixed Issues

### Critical Fixes
1. **Syntax Error in `get-pm-counters.sh`**: Fixed missing spaces in bash conditionals
   ```bash
   # Before (broken)
   if [$VER == 25]; then
   
   # After (fixed)
   if [ $VER == 25 ]; then
   ```

### General Improvements
1. **Error Handling**: Added comprehensive error handling for all kubectl operations
2. **Input Validation**: Better validation of user inputs
3. **Resource Management**: Proper cleanup of temporary files
4. **User Feedback**: Enhanced progress reporting and status messages

## 🛠️ Usage

### Enhanced Archive Logs Downloader

```bash
python3 download_archive_logs_enhanced.py
```

**Features:**
- Environment validation before operations
- File size display
- Progress tracking
- Better error messages
- Automatic compression and cleanup

### Enhanced Debug Logs Downloader

```bash
python3 download_debug_logs_enhanced.py
```

**Features:**
- Environment validation before operations
- File size display for .tgz files
- Progress tracking
- Better error messages
- Automatic compression and cleanup
- Download all debug files option

### Common Utilities

```python
from common_utils import (
    validate_namespace,
    safe_kubectl_exec,
    create_backup_directory,
    log_operation
)
```

**Available Functions:**
- `validate_namespace(namespace)`: Check if namespace exists
- `validate_pod_exists(namespace, pod)`: Check if pod exists
- `safe_kubectl_exec(cmd, timeout)`: Execute kubectl with error handling
- `create_backup_directory(base_name)`: Create timestamped directories
- `log_operation(operation, details)`: Log operations with timestamps

## ⚙️ Configuration

Edit `config.yaml` to customize:
- Default namespace
- Pod names
- File paths
- Timeout values
- UI preferences

## 🔍 Monitoring Scripts

### Enhanced Pod Monitoring

```bash
# English version
./monitor-pods_en.sh [namespace]

# Non-English version  
./monitor-pods.sh [namespace]
```

**Features:**
- Real-time pod status monitoring
- Timestamped output
- Wide format display

### PM Counters Script

```bash
./get-pm-counters.sh [namespace] [version]
```

**Features:**
- Downloads PM counter files from ESYMAC pods
- Supports version 25 and 18 paths
- Creates organized directory structure

## 🧪 VNF Settings Checker

```bash
python3 VNFsettings.py --namespace <namespace> [--25.7] [--username <user>] [--password <pass>]
```

**Features:**
- Checks VNF settings and versions
- Compares MAJOR/MINOR versions
- Supports different authentication methods

## 📊 Best Practices

### Error Handling
- Always validate environment before operations
- Use timeout values for long-running operations
- Provide clear error messages to users

### User Experience
- Show progress for long operations
- Confirm before destructive operations
- Provide helpful feedback and suggestions

### Resource Management
- Clean up temporary files automatically
- Use timestamped directories to avoid conflicts
- Check disk space before large operations

## 🐛 Troubleshooting

### Common Issues

1. **Namespace not found**
   ```
   ❌ Error: Namespace 'ncp0113' does not exist
   ```
   **Solution**: Verify namespace exists with `kubectl get namespaces`

2. **Pod not found**
   ```
   ❌ Error: Pod 'logsreceiver-0' not found in namespace 'ncp0113'
   ```
   **Solution**: Check pod status with `kubectl get pods -n ncp0113`

3. **Permission denied**
   ```
   ❌ Error copying file: permission denied
   ```
   **Solution**: Check kubectl permissions and pod access

4. **Timeout errors**
   ```
   ❌ Command timed out after 30 seconds
   ```
   **Solution**: Increase timeout in config.yaml or check network connectivity

### Debug Mode

Enable verbose logging by setting environment variable:
```bash
export LCM_DEBUG=1
python3 download_archive_logs_enhanced.py
```

## 🔄 Migration from Original Scripts

The enhanced scripts are backward compatible with the original versions. Key differences:

1. **Better error messages**: More descriptive error information
2. **Progress tracking**: Visual feedback during operations
3. **Validation**: Pre-operation environment checks
4. **Logging**: Comprehensive operation logging

## 📈 Performance Improvements

- **Parallel operations**: Where possible, operations run in parallel
- **Caching**: File lists are cached to reduce kubectl calls
- **Compression**: Automatic compression reduces storage requirements
- **Cleanup**: Automatic cleanup reduces disk usage

## 🤝 Contributing

When adding new features or fixing issues:

1. **Follow the pattern**: Use the common utilities for consistency
2. **Add logging**: Include appropriate log messages
3. **Test thoroughly**: Validate with different scenarios
4. **Update documentation**: Keep this README current

## 📝 Changelog

### v2.0.0 (Enhanced Version)
- ✅ Fixed critical syntax error in get-pm-counters.sh
- ✅ Added comprehensive error handling
- ✅ Enhanced user experience with progress indicators
- ✅ Added common utilities module
- ✅ Improved logging and feedback
- ✅ Added configuration file support
- ✅ Enhanced file operations with validation

### v1.0.0 (Original Version)
- Initial release with basic functionality
- English and non-English versions
- Basic file download capabilities 
import os
import subprocess
import re
from datetime import datetime, timedelta
import tarfile

def get_namespace():
    NS = input("Enter namespace (default 'ncp0113'): ") or "ncp0113"
    print(f"Namespace: {NS}")
    return NS

def list_debug_files(NS, pod, path):
    exec_cmd = [
        "kubectl", "-n", NS, "exec", "-it", pod, "--", "ls", "-1", path
    ]
    files_proc = subprocess.run(exec_cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, check=True)
    files = [line.strip() for line in files_proc.stdout.strip().splitlines() if line.endswith('.tgz')]
    return files

def extract_datetime(fname):
    match = re.search(r'(\d{4})_(\d{2})_(\d{2})T(\d{2})_(\d{2})_(\d{2})', fname)
    if match:
        dt_str = f"{match.group(1)}-{match.group(2)}-{match.group(3)} {match.group(4)}:{match.group(5)}:{match.group(6)}"
        return datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
    return None

def get_min_max_dates(all_files):
    # Extract dates from file names and find the earliest and latest date
    dates = [extract_datetime(file) for file in all_files if extract_datetime(file) is not None]
    if dates:
        min_date = min(dates)
        max_date = max(dates)
        return min_date, max_date
    return None, None

def show_menu():
    print("""
    ==== MENU ====
    1. Show all files
    2. Download file by number
    3. Download files by number range (e.g. 25-30)
    4. Download files by date range (and time)
    5. Download files from last 'x' hours or minutes (e.g. 30m)
    6. Download all debug files
    0. Exit
    """)

def show_files_with_numbers(all_files):
    print("\nFile list:")
    for index, file in enumerate(all_files, 1):
        print(f"{index}. {file}")
    print(f"\nFile range: 1 - {len(all_files)}")

def create_dynamic_working_directory():
    # Create a working directory named 'debug_logs_<date and time>'
    current_time = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    working_dir = f"debug_logs_{current_time}"
    if not os.path.exists(working_dir):
        os.makedirs(working_dir)
    return working_dir

def copy_file_to_local(NS, pod, file_name, local_dir):
    local_path = os.path.join(local_dir, os.path.basename(file_name))  # Local path for saving the file
    
    # Make sure the working directory exists
    if not os.path.exists(local_dir):
        os.makedirs(local_dir)
    
    cmd = [
        "kubectl", "-n", NS, "cp",
        f"{pod}:{file_name}",
        local_path  # Copy to local path
    ]
    try:
        subprocess.check_output(cmd)
        print(f"File {file_name} has been copied to folder {local_path}")
    except subprocess.CalledProcessError as e:
        print(f"Error copying file {file_name}. Check namespace/pod. {e}")

def compress_files_to_tar_gz(local_dir):
    local_path = os.path.join(os.getcwd(), local_dir)  # Instead of "/opt/logstore", use local path

    tar_name = f"{local_path}.tar.gz"
    with tarfile.open(tar_name, 'w:gz') as tarf:
        for root, dirs, files in os.walk(local_path):
            for file in files:
                tarf.add(os.path.join(root, file), os.path.relpath(os.path.join(root, file), local_path))
    print(f"Files have been compressed to: {tar_name}")

def remove_local_dir(local_dir):
    local_path = os.path.join(os.getcwd(), local_dir)
    
    if os.path.exists(local_path):
        for root, dirs, files in os.walk(local_path, topdown=False):
            for name in files:
                os.remove(os.path.join(root, name))
            for name in dirs:
                os.rmdir(os.path.join(root, name))
        os.rmdir(local_path)
        print(f"Working folder {local_path} has been deleted.")
    else:
        print(f"Working folder {local_path} does not exist.")

def get_files(NS, pod, path):
    files = list_debug_files(NS, pod, path)
    return files

def download_all_debug_files(NS, pod, path):
    # Create a working directory with a unique name based on date and time
    local_dir = create_dynamic_working_directory()
    
    all_files = get_files(NS, pod, path)
    if not all_files:
        print("No files to download.")
        return
    
    for file in all_files:
        print(f"Downloading file {file}")
        copy_file_to_local(NS, pod, os.path.join(path, file), local_dir)  # Use full path
    
    compress_files_to_tar_gz(local_dir)
    remove_local_dir(local_dir)

def copy_files_by_date(NS, pod, start_date, end_date, all_files, local_dir, path):
    date_filtered_files = [
        file for file in all_files
        if extract_datetime(file) and start_date <= extract_datetime(file) <= end_date
    ]
    
    print(f"Downloading files from {start_date} to {end_date}:")
    for file_name in date_filtered_files:
        print(f"Downloading file {file_name}")
        copy_file_to_local(NS, pod, os.path.join(path, file_name), local_dir)

def copy_files_last_hours(NS, pod, time_period, all_files, local_dir):
    # Create a working directory with a unique name based on date and time
    local_dir = create_dynamic_working_directory()
    
    if time_period.endswith('m'):
        minutes = int(time_period[:-1])
        hours = minutes // 60
        minutes = minutes % 60
    else:
        hours = int(time_period.rstrip('h'))
        minutes = 0

    current_time = datetime.now()
    start_time = current_time - timedelta(hours=hours, minutes=minutes)

    filtered_files = [
        file for file in all_files
        if extract_datetime(file) and extract_datetime(file) >= start_time
    ]

    print(f"Downloading files from the last {hours} hours and {minutes} minutes:")
    for file_name in filtered_files:
        copy_file_to_local(NS, pod, file_name, local_dir)
    
    compress_files_to_tar_gz(local_dir)
    remove_local_dir(local_dir)

def copy_files_by_number(NS, pod, range_str, all_files, local_dir, path):
    try:
        # Parse the number range
        start_num, end_num = map(int, range_str.split('-'))
        if 1 <= start_num <= end_num <= len(all_files):
            files_to_copy = all_files[start_num-1:end_num]
            for file_name in files_to_copy:
                print(f"Downloading file {file_name}")
                copy_file_to_local(NS, pod, os.path.join(path, file_name), local_dir)  # Copy files
        else:
            print("Invalid number range.")
    except ValueError:
        print("Invalid range format. Use format like 25-30.")

def main():
    NS = get_namespace()
    pod = "logsreceiver-0"
    path = "/opt/logstore/transfer/debug/"  # Only change location in container

    while True:
        show_menu()
        choice = input("Choose option: ").strip()

        if choice == "1":
            # In option 1 we don't create working directory, we just list files
            all_files = get_files(NS, pod, path)
            show_files_with_numbers(all_files)
        elif choice == "2":
            local_dir = create_dynamic_working_directory()
            all_files = get_files(NS, pod, path)
            show_files_with_numbers(all_files)
            file_number = int(input("Enter file number: ")) - 1
            if 0 <= file_number < len(all_files):
                copy_file_to_local(NS, pod, path + all_files[file_number], local_dir)
                compress_files_to_tar_gz(local_dir)
                remove_local_dir(local_dir)
            else:
                print("Invalid file number.")
        elif choice == "3":
            range_str = input("Enter file number range (e.g. 25-30): ").strip()
            local_dir = create_dynamic_working_directory()
            all_files = get_files(NS, pod, path)
            copy_files_by_number(NS, pod, range_str, all_files, local_dir, path)  # Pass path
            compress_files_to_tar_gz(local_dir)
            remove_local_dir(local_dir)
        elif choice == "4":
            all_files = get_files(NS, pod, path)
            min_date, max_date = get_min_max_dates(all_files)

            if min_date and max_date:
                print(f"Available date range: from {min_date} to {max_date}")
                start_date = input("Enter start date and time (YYYY-MM-DD HH:MM:SS): ")
                end_date = input("Enter end date and time (YYYY-MM-DD HH:MM:SS): ")
                local_dir = create_dynamic_working_directory()
                try:
                    dt_from = datetime.strptime(start_date, "%Y-%m-%d %H:%M:%S")
                    dt_to = datetime.strptime(end_date, "%Y-%m-%d %H:%M:%S")
                    copy_files_by_date(NS, pod, dt_from, dt_to, all_files, local_dir, path)
                    compress_files_to_tar_gz(local_dir)
                    remove_local_dir(local_dir)
                except ValueError:
                    print("Invalid date format!")
            else:
                print("No available files to process.")
        elif choice == "5":
            time_period = input("How many last hours or minutes do you want to download? (e.g. 30m, 2h): ").strip()
            all_files = get_files(NS, pod, path)
            copy_files_last_hours(NS, pod, time_period, all_files, "")
        elif choice == "6":
            # Option 6 now works automatically, downloads all debug files
            download_all_debug_files(NS, pod, path)
        elif choice == "0":
            print("Finished.")
            break
        else:
            print("Invalid choice. Try again.")

if __name__ == "__main__":
    main()

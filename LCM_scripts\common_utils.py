#!/usr/bin/env python3
"""
Common utilities for LCM scripts
Provides error handling, validation, and shared functionality
"""

import subprocess
import sys
import os
from typing import Optional, Tuple, List


def validate_namespace(namespace: str) -> bool:
    """Validate if namespace exists in Kubernetes"""
    try:
        cmd = ["kubectl", "get", "namespace", namespace]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return namespace in result.stdout
    except subprocess.CalledProcessError:
        return False


def validate_pod_exists(namespace: str, pod_name: str) -> bool:
    """Validate if pod exists in namespace"""
    try:
        cmd = ["kubectl", "-n", namespace, "get", "pod", pod_name]
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        return pod_name in result.stdout
    except subprocess.CalledProcessError:
        return False


def safe_kubectl_exec(cmd: List[str], timeout: int = 30) -> Tuple[bool, str, str]:
    """
    Safely execute kubectl command with timeout and error handling
    
    Returns:
        Tuple[bool, str, str]: (success, stdout, stderr)
    """
    try:
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=timeout,
            check=True
        )
        return True, result.stdout, result.stderr
    except subprocess.TimeoutExpired:
        return False, "", f"Command timed out after {timeout} seconds"
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr
    except FileNotFoundError:
        return False, "", "kubectl command not found. Please ensure kubectl is installed and in PATH"
    except Exception as e:
        return False, "", f"Unexpected error: {str(e)}"


def create_backup_directory(base_name: str = "logs") -> str:
    """Create a timestamped backup directory"""
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    dir_name = f"{base_name}_{timestamp}"
    
    if not os.path.exists(dir_name):
        os.makedirs(dir_name)
    
    return dir_name


def log_operation(operation: str, details: str = "", level: str = "INFO"):
    """Log operations with timestamp"""
    from datetime import datetime
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"[{timestamp}] {level}: {operation} {details}")


def confirm_operation(prompt: str = "Continue? (y/N): ") -> bool:
    """Get user confirmation for operations"""
    response = input(prompt).strip().lower()
    return response in ['y', 'yes']


def format_file_size(size_bytes: int) -> str:
    """Format file size in human readable format"""
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size_bytes < 1024.0:
            return f"{size_bytes:.1f} {unit}"
        size_bytes /= 1024.0
    return f"{size_bytes:.1f} TB"


def validate_date_format(date_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> bool:
    """Validate date string format"""
    from datetime import datetime
    try:
        datetime.strptime(date_str, format_str)
        return True
    except ValueError:
        return False


def get_available_disk_space(path: str = ".") -> int:
    """Get available disk space in bytes"""
    try:
        stat = os.statvfs(path)
        return stat.f_frsize * stat.f_bavail
    except OSError:
        return 0


def check_disk_space_requirements(required_bytes: int, path: str = ".") -> bool:
    """Check if there's enough disk space"""
    available = get_available_disk_space(path)
    return available >= required_bytes 